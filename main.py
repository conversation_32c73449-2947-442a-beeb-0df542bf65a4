import customtkinter as ctk
from tkinter import messagebox, filedialog
import qrcode
from PIL import Image, ImageTk
from datetime import datetime
from typing import Optional
from pyzbar.pyzbar import decode

# Set appearance mode and color theme
ctk.set_appearance_mode("light")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"


class QRCodeGenerator:
    def __init__(self, root: ctk.CTk) -> None:
        self.root = root
        self.root.title("QR Code Generator & Decoder")
        self.root.geometry("550x700")
        self.root.minsize(500, 650)

        # Variables
        self.qr_image_pil: Optional[Image.Image] = None
        self.qr_image_tk: Optional[ImageTk.PhotoImage] = None
        self.display_size = 350

        # Color scheme
        self.colors = {
            "Blue": "#2196F3",
            "Red": "#F44336",
            "Green": "#4CAF50",
            "Purple": "#9C27B0",
            "Orange": "#FF9800",
            "Black": "#212121"
        }
        self.selected_color = ctk.StringVar(value="Blue")

        self.create_widgets()
        self.center_window()


    def center_window(self) -> None:
        """Center the application window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self) -> None:
        """Create a modern, clean UI layout."""
        # Main Content with Tabs using CTkTabview
        self.tabview = ctk.CTkTabview(self.root, width=450, height=500)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)

        # Add tabs
        self.tabview.add("Generate QR")
        self.tabview.add("Decode QR")

        # Generator Tab Content
        generator_tab = self.tabview.tab("Generate QR")

        # Input Section
        input_frame = ctk.CTkFrame(generator_tab)
        input_frame.pack(fill="x", pady=(0, 20), padx=10)

        # URL/Text input
        url_label = ctk.CTkLabel(input_frame, text="Enter URL or Text:", font=ctk.CTkFont(size=12, weight="bold"))
        url_label.pack(pady=(10, 5), padx=10, anchor="w")

        self.url_entry = ctk.CTkEntry(input_frame, placeholder_text="Enter text or URL here...", width=350)
        self.url_entry.pack(pady=(0, 10), padx=10, fill="x")

        # Color selection
        color_frame = ctk.CTkFrame(input_frame)
        color_frame.pack(fill="x", pady=(0, 10), padx=10)

        color_label = ctk.CTkLabel(color_frame, text="Color:", font=ctk.CTkFont(size=12, weight="bold"))
        color_label.pack(side="left", padx=(10, 5), pady=10)

        self.color_combo = ctk.CTkComboBox(
            color_frame,
            variable=self.selected_color,
            values=list(self.colors.keys()),
            state="readonly",
            width=120
        )
        self.color_combo.pack(side="left", padx=(5, 10), pady=10)

        # QR Display
        self.qr_display_label = ctk.CTkLabel(
            generator_tab,
            text="",
            width=350,
            height=350,
            fg_color="gray90",
            corner_radius=10
        )
        self.qr_display_label.pack(pady=(10, 15))

        # Buttons Frame - side by side layout
        button_frame = ctk.CTkFrame(generator_tab, fg_color="transparent")
        button_frame.pack(pady=(0, 20), padx=20)

        # Generate Button
        self.generate_btn = ctk.CTkButton(
            button_frame,
            text="Generate QR",
            command=self.generate_qr,
            width=150,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.generate_btn.pack(side="left", padx=(0, 10), pady=10)

        # Save Button
        self.save_btn = ctk.CTkButton(
            button_frame,
            text="Save QR",
            command=self.save_qr,
            state="disabled",
            width=150,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.save_btn.pack(side="left", padx=(10, 0), pady=10)

        # Decoder Tab Content
        decoder_tab = self.tabview.tab("Decode QR")

        # Decoder buttons - side by side layout
        decoder_button_frame = ctk.CTkFrame(decoder_tab, fg_color="transparent")
        decoder_button_frame.pack(pady=(20, 10), padx=20)

        self.upload_btn = ctk.CTkButton(
            decoder_button_frame,
            text="Upload Image",
            command=self.upload_image,
            width=150,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.upload_btn.pack(side="left", padx=(0, 10), pady=10)

        self.decode_btn = ctk.CTkButton(
            decoder_button_frame,
            text="Decode QR",
            command=self.decode_qr,
            state="disabled",
            width=150,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.decode_btn.pack(side="left", padx=(10, 0), pady=10)

        # Decoded QR Display
        self.decoded_image_label = ctk.CTkLabel(
            decoder_tab,
            text="",
            width=350,
            height=350,
            fg_color="gray90",
            corner_radius=10
        )
        self.decoded_image_label.pack(pady=(10, 15))

        # Decoded Text Display
        self.decoded_text = ctk.CTkTextbox(
            decoder_tab,
            height=120,
            width=400,
            font=ctk.CTkFont(size=12)
        )
        self.decoded_text.pack(pady=(0, 20), padx=20, fill="x")
        self.decoded_text.configure(state="disabled")

        # Bind events
        self.color_combo.configure(command=self.update_color)
        self.url_entry.bind('<Return>', lambda _: self.generate_qr())
        self.url_entry.focus()

    def update_color(self, _=None) -> None:
        """Update the QR code color based on selection."""
        if self.qr_image_pil:
            self.generate_qr()  # Regenerate QR code with new color

    def update_qr_display(self) -> None:
        """Updates the label with the generated QR code image."""
        if self.qr_image_pil:
            img_resized = self.qr_image_pil.resize(
                (self.display_size, self.display_size), Image.Resampling.LANCZOS
            )
            self.qr_image_ctk = ctk.CTkImage(light_image=img_resized, size=(self.display_size, self.display_size))
            self.qr_display_label.configure(image=self.qr_image_ctk)
            self.save_btn.configure(state="normal")
        else:
            self.save_btn.configure(state="disabled")

    def generate_qr(self) -> None:
        """Generates the QR code from the input data."""
        data = self.url_entry.get().strip()
        if not data:
            messagebox.showerror("Error", "Input data cannot be empty.")
            return

        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_H,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            # Ensure image is RGB for broader compatibility (e.g. with saving as JPG)
            self.qr_image_pil = qr.make_image(
                fill_color=self.colors[self.selected_color.get()], back_color="white"
            ).convert("RGB")
            self.update_qr_display()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate QR code: {str(e)}")
            self.qr_image_pil = None  # Reset on failure
            self.update_qr_display()

    def save_qr(self) -> None:
        """Saves the generated QR code to a file."""
        if not self.qr_image_pil:
            messagebox.showwarning("No QR Code", "Please generate a QR code first.")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            initialfile=f"qrcode_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("All files", "*.*"),
            ],
        )

        if file_path:
            try:
                self.qr_image_pil.save(file_path)
                messagebox.showinfo(
                    "Success", f"QR Code saved successfully to:\n{file_path}"
                )
            except Exception as e:
                messagebox.showerror("Save Error", f"Failed to save QR code: {str(e)}")

    def upload_image(self) -> None:
        """Handle image upload for QR code decoding."""
        file_path = filedialog.askopenfilename(
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                # Load and display the image
                image = Image.open(file_path)
                image.thumbnail((self.display_size, self.display_size))
                self.decoded_image_ctk = ctk.CTkImage(light_image=image, size=(self.display_size, self.display_size))
                self.decoded_image_label.configure(image=self.decoded_image_ctk, text="")
                self.decode_btn.configure(state="normal")
                self.uploaded_image = image
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")

    def decode_qr(self) -> None:
        """Decode QR code from the uploaded image."""
        if not hasattr(self, 'uploaded_image'):
            messagebox.showwarning("No Image", "Please upload an image first.")
            return

        try:
            # Decode QR code
            decoded_objects = decode(self.uploaded_image)

            if not decoded_objects:
                messagebox.showinfo("No QR Code", "No QR code found in the image.")
                return

            # Display decoded text
            self.decoded_text.configure(state="normal")
            self.decoded_text.delete("1.0", "end")
            for obj in decoded_objects:
                self.decoded_text.insert("end", obj.data.decode('utf-8') + '\n')
            self.decoded_text.configure(state="disabled")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to decode QR code: {str(e)}")


def main() -> None:
    root = ctk.CTk()
    QRCodeGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
